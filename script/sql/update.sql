ALTER TABLE `cm_change_info`
    MODIFY COLUMN `change_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更原因' AFTER `temporary_url`,
    MODIFY COLUMN `change_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更描述' AFTER `change_reason`,
    MODIFY COLUMN `special_remark` varchar (500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '特殊备注' AFTER `email_content`,
    MODIFY COLUMN `affected_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '影响描述' AFTER `file_url`;

ALTER TABLE `cm_network_freeze_area`
    MODIFY COLUMN `area_name` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封网地区' AFTER `area_id`,
    ADD COLUMN `area_name_en` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封网地区-英文' AFTER `area_name`;

ALTER TABLE `cm_conf_info `
    ADD COLUMN `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称-英文' AFTER `status`;
